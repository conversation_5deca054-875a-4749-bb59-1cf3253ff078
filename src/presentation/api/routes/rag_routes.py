"""
知识库模块 API路由
知识库CRUD（创建、删除、详情查看、最近活动、修改详情）
知识库中的文件管理（列表、添加、解绑）
知识库中的会话管理（列表、添加、解绑、预览）会话中制品特殊处理
"""

import logging

from fastapi import (
    APIRouter,
    Query,
    Depends,
)

from src.application.rag_api_models import *
from src.domain.services.auth_service import AuthContext, require_auth
from src.domain.services.knowledge_service import knowledgebase_service
from src.domain.utils.check_utils import CheckUtils
from src.presentation.api.dependencies.api_common_utils import *

router = APIRouter(prefix="/api", tags=["rag"])


# ==================== 知识库操作 ====================


@router.post("/knowledge_base/create")
async def create_knowledge_base(
    request: KnowledgeBaseCreateRequest,
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
):
    """创建知识库"""
    try:
        # 检查参数
        CheckUtils.parameter_not_null(request.name, "name")
        CheckUtils.parameter_not_null(current_user.ali_uid, "ali_uid")
        CheckUtils.parameter_not_null(current_user.wy_id, "wy_id")

        logger.info(f"[API] 创建知识库: {request}")
        kb_id = knowledgebase_service.create_knowledge_base(
            name=request.name,
            auth_context=current_user,
            description=request.description,
        )
        return package_api_result(
            data=KnowledgeBaseCreateResponse(kb_id=kb_id), request_id=request_id
        )

    except Exception as e:
        return handle_exception(e, request_id)


@router.get("/knowledge_base/list")
async def list_knowledge_bases(
    max_results: int = Query(10, description="每页最大结果数"),
    next_token: Optional[str] = Query(None, description="分页Token"),
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
):
    """查询知识库列表"""
    try:
        # 检查参数
        CheckUtils.parameter_not_null(max_results, "max_result")
        CheckUtils.parameter_not_null(current_user.ali_uid, "ali_uid")
        CheckUtils.parameter_not_null(current_user.wy_id, "wy_id")

        logger.info(f"[API] 查询知识库列表: {max_results}, {next_token}")
        response = knowledgebase_service.list_knowledge_bases(
            current_user.ali_uid, current_user.wy_id, max_results, next_token
        )
        return package_api_result(
            data=response.data,
            total_count=response.max_results,
            next_token=response.next_token,
            request_id=request_id,
        )
    except Exception as e:
        return handle_exception(e, request_id)


@router.get("/knowledge_base/get")
async def get_knowledge_base(
    kb_id: str = Query(..., description="知识库ID"),
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
):
    """查询知识库详情"""
    try:
        # 检查参数
        CheckUtils.parameter_not_null(kb_id, "kb_id")

        logger.info(f"[API] 查询知识库详情: {kb_id}")
        response = knowledgebase_service.get_knowledge_base(
            kb_id=kb_id,
            auth_context=current_user,
        )
        return package_api_result(data=response, request_id=request_id)
    except Exception as e:
        return handle_exception(e, request_id)


@router.post("/knowledge_base/update")
async def update_knowledge_base(
    request: KnowledgeBaseUpdateRequest,
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
):
    """修改知识库信息"""
    try:
        # 检查参数
        CheckUtils.parameter_not_null(request.kb_id, "kb_id")
        CheckUtils.parameter_not_null(request.name, "name")
        CheckUtils.parameter_not_null(current_user.ali_uid, "ali_uid")
        CheckUtils.parameter_not_null(current_user.wy_id, "wy_id")

        logger.info(f"[API] 修改知识库信息: {request}")
        knowledgebase_service.update_knowledge_base(
            kb_id=request.kb_id,
            auth_context=current_user,
            name=request.name,
            description=request.description,
        )
        return package_api_result(request_id=request_id)
    except Exception as e:
        return handle_exception(e, request_id)


@router.post("/knowledge_base/delete")
async def delete_knowledge_base(
    request: KnowledgeBaseDeleteRequest,
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
):
    """删除知识库"""
    try:
        # 检查参数
        CheckUtils.parameter_not_null(request.kb_id, "kb_id")
        logger.info(f"[API] 删除知识库: {request.kb_id}")
        knowledgebase_service.delete_knowledge_base(
            kb_id=request.kb_id,
            auth_context=current_user,
        )
        return package_api_result(request_id=request_id)
    except Exception as e:
        return handle_exception(e, request_id)


# ==================== 知识库文档操作 ====================


@router.post("/knowledge_base/document/create")
async def create_documents(
    request: KnowledgeBaseDocumentCreateRequest,
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
):
    """创建文档"""
    try:
        # 检查参数
        CheckUtils.parameter_not_null(request.kb_id, "kb_id")
        CheckUtils.parameter_not_empty(request.document_list, "document_list")

        logger.info(
            f"[API] 创建文档: {request.kb_id}, 文档数量: {len(request.document_list)}"
        )

        knowledgebase_service.create_documents(
            auth_context=current_user,
            kb_id=request.kb_id,
            document_list=request.document_list,
        )
        return package_api_result(request_id=request_id)
    except Exception as e:
        return handle_exception(e, request_id)


@router.get("/knowledge_base/document/list")
async def list_documents(
    max_results: int = Query(10, description="每页最大结果数"),
    next_token: Optional[str] = Query(None, description="分页Token"),
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
    kb_id: str = Query(..., description="知识库ID"),
):
    """查询文档列表"""
    try:
        # 检查参数
        CheckUtils.parameter_not_null(kb_id, "kb_id")

        logger.info(
            f"[API] 查询文档列表: {kb_id}, 每页最大结果数: {max_results}, 分页Token: {next_token}"
        )

        response = knowledgebase_service.list_documents(
            auth_context=current_user,
            kb_id=kb_id,
            max_results=max_results,
            next_token=next_token,
        )
        return package_api_result(
            data=response.data,
            total_count=response.max_results,
            next_token=response.next_token,
            request_id=request_id,
        )
    except Exception as e:
        return handle_exception(e, request_id)


@router.get("/knowledge_base/document/describe")
async def describe_documents(
    kb_id: str = Query(..., description="知识库ID"),
    document_id: str = Query(..., description="文档ID"),
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
):
    """描述文档"""
    try:
        # 检查参数
        CheckUtils.parameter_not_null(kb_id, "kb_id")
        CheckUtils.parameter_not_null(document_id, "document_id")

        logger.info(f"[API] 描述文档: {kb_id}, 文档ID: {document_id}")

        response = knowledgebase_service.describe_document(
            auth_context=current_user,
            kb_id=kb_id,
            file_id=document_id,
        )
        return package_api_result(data=response, request_id=request_id)
    except Exception as e:
        return handle_exception(e, request_id)


@router.post("/knowledge_base/document/delete")
async def delete_documents(
    request: KnowledgeBaseDocumentDeleteRequest,
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
):
    """删除文档"""
    try:
        # 检查参数
        CheckUtils.parameter_not_null(request.kb_id, "kb_id")
        CheckUtils.parameter_not_empty(request.document_id_list, "document_id_list")

        logger.info(f"[API] 删除文档: {request.kb_id}, 文档ID: {request.document_id_list}")

        knowledgebase_service.delete_documents(
            auth_context=current_user,
            kb_id=request.kb_id,
            document_id_list=request.document_id_list,
        )
        return package_api_result(request_id=request_id)
    except Exception as e:
        return handle_exception(e, request_id)


# ==================== 知识库会话操作 ====================


@router.post("/knowledge_base/session/create")
async def create_sessions(
    request: KnowledgeBaseSessionCreateRequest,
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
):
    """创建会话"""
    try:
        # 检查参数
        CheckUtils.parameter_not_empty(request.kb_id, "kb_id")
        CheckUtils.parameter_not_empty(request.session_id, "session_id")
        # 安全地获取列表长度，处理 None 的情况
        message_count = len(request.message_list) if request.message_list else 0
        file_count = len(request.file_list) if request.file_list else 0
        CheckUtils.parameter_validate(
            message_count > 0 or file_count > 0, "message_list"
        )
        logger.info(
            f"[API] 创建会话: {request.kb_id}, 会话ID: {request.session_id}, 消息数量: {message_count}, 文件数量: {file_count}"
        )

        # 创建会话
        knowledgebase_service.create_sessions(
            auth_context=current_user,
            kb_id=request.kb_id,
            session_id=request.session_id,
            message_list=request.message_list,
            file_list=request.file_list,
        )
        return package_api_result(request_id=request_id)
    except Exception as e:
        return handle_exception(e, request_id)


@router.get("/knowledge_base/session/list")
async def list_sessions(
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
    max_results: int = Query(10, description="每页最大结果数"),
    next_token: Optional[str] = Query(None, description="分页Token"),
    kb_id: str = Query(..., description="知识库ID"),
):
    """查询会话列表"""
    try:
        # 检查参数
        CheckUtils.parameter_not_empty(kb_id, "kb_id")
        CheckUtils.parameter_not_null(max_results, "max_results")

        logger.info(
            f"[API] 查询会话列表: {kb_id}, 每页最大结果数: {max_results}, 分页Token: {next_token}"
        )

        response = knowledgebase_service.list_sessions(
            auth_context=current_user,
            kb_id=kb_id,
            max_results=max_results,
            next_token=next_token,
        )
        return package_api_result(
            data=response.data,
            total_count=response.max_results,
            next_token=response.next_token,
            request_id=request_id,
        )
    except Exception as e:
        return handle_exception(e, request_id)


@router.get("/knowledge_base/session/message/id_list")
async def get_kb_session_message_ids(
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
    kb_id: str = Query(..., description="知识库ID"),
    session_id: str = Query(..., description="会话ID"),
):
    """获取指定知识库关联会话的所有消息ID列表"""
    try:
        # 检查参数
        CheckUtils.parameter_not_null(kb_id, "kb_id")
        CheckUtils.parameter_not_null(session_id, "session_id")

        logger.info(
            f"[API] 获取会话消息ID列表: {kb_id}, 会话ID: {session_id}"
        )

        message_ids = knowledgebase_service.get_kb_session_message_ids(
            auth_context=current_user,
            kb_id=kb_id,
            session_id=session_id,
        )

        response = KnowledgeBaseSessionMessageIdsResponse(message_ids=message_ids)
        return package_api_result(
            data=response, request_id=request_id
        )
    except Exception as e:
        return handle_exception(e, request_id)


@router.get("/knowledge_base/session/message/list")
async def list_session_messages(
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
    max_results: int = Query(10, description="每页最大结果数"),
    next_token: Optional[str] = Query(None, description="分页Token"),
    kb_id: str = Query(..., description="知识库ID"),
    session_id: str = Query(..., description="会话ID"),
):
    """查询会话消息列表"""
    try:
        # 检查参数
        CheckUtils.parameter_not_null(kb_id, "kb_id")
        CheckUtils.parameter_not_null(session_id, "session_id")
        CheckUtils.parameter_not_null(max_results, "max_results")

        logger.info(
            f"[API] 查询会话消息列表: {kb_id}, 会话ID: {session_id}, 每页最大结果数: {max_results}, 分页Token: {next_token}"
        )

        response = knowledgebase_service.list_session_messages(
            auth_context=current_user,
            kb_id=kb_id,
            session_id=session_id,
            max_results=max_results,
            next_token=next_token,
        )
        return package_api_result(
            data=response.data,
            total_count=response.max_results,
            next_token=response.next_token,
            request_id=request_id,
        )
    except Exception as e:
        return handle_exception(e, request_id)


@router.get("/knowledge_base/session/file/list")
async def list_session_files(
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
    max_results: int = Query(10, description="每页最大结果数"),
    next_token: Optional[str] = Query(None, description="分页Token"),
    kb_id: str = Query(..., description="知识库ID"),
    session_id: str = Query(..., description="会话ID"),
):
    """创建文档"""
    try:
        # 检查参数
        CheckUtils.parameter_not_null(kb_id, "kb_id")
        CheckUtils.parameter_not_null(session_id, "session_id")
        CheckUtils.parameter_not_null(max_results, "max_results")

        logger.info(
            f"[API] 查询会话附件列表: {kb_id}, 会话ID: {session_id}, 每页最大结果数: {max_results}, 分页Token: {next_token}"
        )

        response = knowledgebase_service.list_session_files(
            auth_context=current_user,
            kb_id=kb_id,
            session_id=session_id,
            max_results=max_results,
            next_token=next_token,
        )
        return package_api_result(
            data=response.data,
            total_count=response.max_results,
            next_token=response.next_token,
            request_id=request_id,
        )
    except Exception as e:
        return handle_exception(e, request_id)


@router.get("/knowledge_base/session/document_relations")
async def list_session_document_relations(
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
    kb_id: str = Query(..., description="知识库ID"),
    session_id: str = Query(..., description="会话ID"),
):
    """获取会话下和知识库关联的所有文档file_id列表"""
    try:
        # 检查参数
        CheckUtils.parameter_not_empty(kb_id, "kb_id")
        CheckUtils.parameter_not_empty(session_id, "session_id")

        logger.info(
            f"[API] 获取会话文档file_id列表: {kb_id}, 会话ID: {session_id}"
        )

        file_ids = knowledgebase_service.list_session_document_relations(
            auth_context=current_user,
            kb_id=kb_id,
            session_id=session_id,
        )
        return package_api_result(data=file_ids, request_id=request_id)
    except Exception as e:
        return handle_exception(e, request_id)


@router.post("/knowledge_base/document/preview")
async def get_document_preview(
    request: KnowledgeBaseDocumentPreviewRequest,
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
):
    """获取知识库文档预览链接"""
    try:
        # 检查参数
        CheckUtils.parameter_not_empty(request.file_id, "file_id")

        logger.info(
            f"[API] 获取文档预览链接: file_id={request.file_id}, "
            f"user_key={current_user.user_key}, "
            f"ali_uid={current_user.ali_uid}, "
            f"wy_id={current_user.wy_id}"
        )

        # 调用知识库服务获取预览链接
        preview_result = knowledgebase_service.get_document_preview_url(
            auth_context=current_user,
            file_id=request.file_id.strip()
        )

        if not preview_result:
            logger.warning(f"[API] 文档不存在或无法预览: file_id={request.file_id}")
            return package_api_result(
                code=404,
                message="文档不存在或无法预览",
                data=None,
                request_id=request_id,
                success=False
            )

        file_name, preview_url, expires_in = preview_result

        # 构造响应
        response = KnowledgeBaseDocumentPreviewResponse(
            file_id=request.file_id,
            file_name=file_name,
            preview_url=preview_url,
            expires_in=expires_in
        )

        logger.info(f"[API] 获取文档预览链接成功: file_id={request.file_id}, expires_in={expires_in}s")
        return package_api_result(data=response, request_id=request_id)
    except Exception as e:
        return handle_exception(e, request_id)


@router.post("/knowledge_base/session/update")
async def update_session(
    request: KnowledgeBaseSessionUpdateRequest,
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
):
    """更新会话"""
    try:
        # 检查参数
        CheckUtils.parameter_not_null(request.kb_id, "kb_id")
        CheckUtils.parameter_not_null(request.session_id, "session_id")
        logger.info(f"[API] 更新会话: {request.kb_id}, 会话ID: {request.session_id}")

        knowledgebase_service.update_session(
            auth_context=current_user,
            kb_id=request.kb_id,
            session_id=request.session_id,
            message_list=request.message_list,
            file_list=request.file_list,
        )
        return package_api_result(request_id=request_id)
    except Exception as e:
        return handle_exception(e, request_id)


@router.post("/knowledge_base/session/delete")
async def delete_session(
    request: KnowledgeBaseSessionDeleteRequest,
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
):
    """删除会话"""
    try:
        # 检查参数
        CheckUtils.parameter_not_null(request.kb_id, "kb_id")
        CheckUtils.parameter_not_null(request.session_id, "session_id")

        logger.info(f"[API] 删除会话: {request.kb_id}, 会话ID: {request.session_id}")

        knowledgebase_service.delete_session(
            auth_context=current_user,
            kb_id=request.kb_id,
            session_id=request.session_id,
        )
        return package_api_result(request_id=request_id)
    except Exception as e:
        return handle_exception(e, request_id)


@router.post("/knowledge_base/sessions/delete")
async def delete_session(
    request: KnowledgeBaseSessionsDeleteRequest,
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
):
    """删除会话"""
    try:
        # 检查参数
        CheckUtils.parameter_not_null(request.kb_id, "kb_id")
        CheckUtils.parameter_not_empty(request.session_list, "session_list")

        logger.info(f"[API] 删除会话: {request.kb_id}, 会话ID: {request.session_list}")

        knowledgebase_service.delete_sessions(
            auth_context=current_user,
            kb_id=request.kb_id,
            session_ids=request.session_list,
        )
        return package_api_result(request_id=request_id)
    except Exception as e:
        return handle_exception(e, request_id)


# ==================== 知识库日志操作 ====================


@router.get("/knowledge_base/log/list")
async def list_logs(
    max_results: int = Query(10, description="每页最大结果数"),
    next_token: Optional[str] = Query(None, description="分页Token"),
    kb_id: Optional[str] = Query(None, description="知识库ID"),
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
):
    """查询日志列表"""
    try:
        # 检查参数
        CheckUtils.parameter_not_null(max_results, "max_results")

        logger.info(
            f"[API] 查询日志列表: {kb_id}, 每页最大结果数: {max_results}, 分页Token: {next_token}"
        )

        response = knowledgebase_service.list_logs(
            auth_context=current_user,
            kb_id=kb_id,
            max_results=max_results,
            next_token=next_token,
        )
        return package_api_result(
            data=response.data,
            total_count=response.max_results,
            next_token=response.next_token,
            request_id=request_id,
        )
    except Exception as e:
        return handle_exception(e, request_id)


# ==================== 状态更新轮询====================


@router.get("/knowledge_base/status/update")
async def update_knowledge_base_status(
    request_id: str = Depends(get_request_id_dependency)
):
    """更新知识库相关资源状态，在Schedulerx中配置每分钟执行一次"""
    try:
        logger.info("[API] 更新知识库状态")
        knowledgebase_service.update_knowledge_base_status()
        return package_api_result(request_id=request_id)
    except Exception as e:
        return handle_exception(e, request_id)
