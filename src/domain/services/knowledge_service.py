#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库业务服务
"""

from math import log
from typing import List, Optional, Tuple
from alibabacloud_wuyingaiinner20250709 import models as rag_models
from domain.models.enums import KbO<PERSON>ationType, KbState, KbTargetType
from loguru import logger

from infrastructure.database.models.knowledgebase_models import (
    KbDocumentModel,
    KbDocumentRelationModel,
    KbOperationLogModel,
    KbSessionModel,
    KnowledgeBaseModel,
)
from infrastructure.database.repositories.kb_document_relations_repository import (
    kb_document_relations_repository,
)
from infrastructure.database.repositories.kb_documents_repository import (
    kb_documents_repository,
)
from infrastructure.database.repositories.kb_sessions_repository import (
    kb_sessions_repository,
)
from infrastructure.database.repositories.knowledgebase_repository import (
    knowledgebase_repository,
)
from infrastructure.database.repositories.kb_operation_logs_repository import (
    kb_operation_logs_repository,
)
from src.application.rag_api_models import *
from src.domain.services.auth_service import AuthContext, auth_service
from src.domain.services.file_service import file_service
from src.domain.utils.check_utils import CheckUtils
from src.domain.utils.next_token_utils import NextTokenUtils
from src.domain.utils.time_utils import TimeUtils
from src.infrastructure.database.models.auth_models import ResourceType, PermissionType
from src.infrastructure.database.saga_transaction import saga_transactional
from src.infrastructure.memory.memory_sdk import memory_sdk
from src.popclients.rag_client import create_rag_client


class KnowledgeService:
    """知识库业务服务"""

    def __init__(self):
        self.kb_operation_logs_repository = kb_operation_logs_repository
        self.knowledgebase_repository = knowledgebase_repository
        self.kb_documents_repository = kb_documents_repository
        self.kb_document_relations_repository = kb_document_relations_repository
        self.kb_sessions_repository = kb_sessions_repository

    # ==================== 知识库和属性关系判断相关操作 ====================

    def is_knowledge_base_session(
        self, kb_id: str, session_id_list: List[str]
    ) -> Dict[str, bool]:
        """
        判断会话是否属于知识库
        """
        CheckUtils.parameter_not_null(kb_id, "kb_id")
        CheckUtils.parameter_not_empty(session_id_list, "session_id_list")

        # 获取会话详情
        sessions = self.kb_sessions_repository.list_sessions(
            kb_id=kb_id,
            session_id_list=session_id_list,
        )
        session_ids_set = set(session.session_id for session in sessions)
        return {
            session_id: session_id in session_ids_set for session_id in session_id_list
        }

    def is_knowledge_base_message(
        self, kb_id: str, session_id: str, message_id_list: List[str]
    ) -> Dict[str, bool]:
        """
        判断消息是否属于知识库
        """
        CheckUtils.parameter_not_null(kb_id, "kb_id")
        CheckUtils.parameter_not_null(session_id, "session_id")
        CheckUtils.parameter_not_empty(message_id_list, "message_id_list")

        # 获取会话详情
        session = self.kb_sessions_repository.get_session(kb_id, session_id)
        CheckUtils.object_exists(session, "session")

        # 获取消息详情
        if session.message_id_list:
            message_ids = set(session.message_id_list.split(","))
            return {
                message_id: message_id in message_ids for message_id in message_id_list
            }
        else:
            return {message_id: False for message_id in message_id_list}

    def is_knowledge_base_file(
        self, kb_id: str, session_id: str, file_id_list: List[str]
    ) -> Dict[str, bool]:
        """
        判断文件是否属于知识库
        """
        CheckUtils.parameter_not_null(session_id, "kb_id")
        CheckUtils.parameter_not_empty(file_id_list, "file_id_list")

        # 获取会话详情
        session = self.kb_sessions_repository.get_session(kb_id, session_id)
        CheckUtils.object_exists(session, "session")

        # 获取知识库文件
        doc_relations = self.kb_document_relations_repository.list_relations(
            kb_id=kb_id,
            session_id=session_id,
            file_id_list=file_id_list,
        )
        file_ids = set(relation.file_id for relation in doc_relations)

        # 判断文件是否属于知识库
        return {file_id: file_id in file_ids for file_id in file_id_list}

    # ==================== 知识库相关操作 ====================

    def create_knowledge_base(
        self,
        name: str,
        auth_context: AuthContext,
        description: Optional[str] = None,
    ) -> str:
        """
        创建知识库（支持 Saga 事务）

        使用 Saga 模式处理分布式事务：
        1. 先调用 RAG 服务创建知识库
        2. 在本地数据库创建记录
        3. 注册资源鉴权
        4. 如果本地操作失败，自动执行补偿操作（删除 RAG 知识库）
        """
        # 查重：同名、同 owner_ali_uid、owner_wy_id（search_knowledge_bases仅支持未删除）
        owner_ali_uid = auth_context.ali_uid
        owner_wy_id = auth_context.wy_id
        existing = self.knowledgebase_repository.list_knowledge_bases(
            owner_ali_uid=owner_ali_uid,
            owner_wy_id=owner_wy_id,
            name=name,
        )
        CheckUtils.object_not_exists_if(len(existing) == 0, "knowledge_base")

        # 步骤1: 调用 RAG 服务创建知识库（事务外部）
        logger.info(f"调用 RAG 服务创建知识库: {name}")
        client = create_rag_client()
        create_rag_response_body = client.create_kb(
            name=name,
            description=description,
            ali_uid=str(owner_ali_uid),
            wy_id=owner_wy_id,
        ).body

        # 检查创建结果是否成功，以及 kb_id 是否存在
        if (
            not create_rag_response_body
            or create_rag_response_body.success is False
            or not getattr(create_rag_response_body, "data", None)
            or not getattr(create_rag_response_body.data, "kb_id", None)
        ):
            # 业务逻辑错误，不需要事务回滚
            raise ValueError(f"RAG服务创建知识库失败: {create_rag_response_body.code}")

        kb_id = create_rag_response_body.data.kb_id
        logger.info(f"RAG服务创建知识库成功: {kb_id}")

        # 步骤2: 使用事务处理本地数据库操作和资源注册
        return self._create_knowledge_base_with_transaction(
            kb_id=kb_id,
            name=name,
            owner_ali_uid=owner_ali_uid,
            owner_wy_id=owner_wy_id,
            description=description,
            auth_context=auth_context,
            client=client,
        )

    @saga_transactional(rollback_on=Exception)  # 排除 ValueError，只对系统异常回滚
    def _create_knowledge_base_with_transaction(
        self,
        kb_id: str,
        name: str,
        owner_ali_uid: int,
        owner_wy_id: str,
        description: Optional[str],
        auth_context: AuthContext,
        client,
        **kwargs,
    ) -> str:
        """
        使用事务创建知识库的本地部分
        """
        # 添加补偿操作：如果后续步骤失败，删除 RAG 知识库
        _saga_manager = kwargs.get("_saga_manager")
        if _saga_manager:

            def delete_rag_kb():
                try:
                    logger.info(f"执行补偿操作：删除 RAG 知识库 {kb_id}")
                    client.delete_kb(kb_id=kb_id)
                    logger.info(f"补偿操作成功：删除 RAG 知识库 {kb_id}")
                except Exception as e:
                    logger.error(f"补偿操作失败：无法删除 RAG 知识库 {kb_id}: {e}")

            _saga_manager.add_compensation_action(delete_rag_kb)

        # 步骤2: 在本地数据库创建记录
        logger.info(f"在本地数据库创建记录: {kb_id}")
        self.knowledgebase_repository.create_knowledge_base(
            kb_id=kb_id,
            name=name,
            owner_ali_uid=owner_ali_uid,
            owner_wy_id=owner_wy_id,
            description=description,
        )

        # 步骤3: 注册资源鉴权
        logger.info(f"注册资源鉴权: {kb_id}")
        auth_service.register_resource(
            context=auth_context,
            resource_type=ResourceType.KNOWLEDGE_BASE,
            resource_id=str(kb_id),
            resource_name=name,
            is_public=False,
        )

        logger.info(f"知识库创建成功: {kb_id}")
        return kb_id

    """查询知识库列表"""

    def list_knowledge_bases(
        self, owner_ali_uid: int, owner_wy_id: str, max_result: int, next_token: str
    ) -> KnowledgeBaseListPaginationResponse:

        # 解析 next_token
        pagination_model = NextTokenUtils.decode(next_token)
        less_than_equal_gmt_create = pagination_model.gmt
        less_than_equal_id = pagination_model.id

        # 分页搜索知识库
        dbs = self.knowledgebase_repository.list_knowledge_bases(
            owner_ali_uid=owner_ali_uid,
            owner_wy_id=owner_wy_id,
            limit=max_result + 1,
            less_than_equal_id=less_than_equal_id,
            less_than_equal_gmt_create=less_than_equal_gmt_create,
            order_pairs=[
                KnowledgeBaseModel.gmt_created.desc(),
                KnowledgeBaseModel.id.desc(),
            ],
        )

        # 统计知识库数量
        count = self.knowledgebase_repository.count_knowledge_bases_by_owner(
            owner_ali_uid=owner_ali_uid, owner_wy_id=owner_wy_id
        )

        # 生成 next_token
        next_token = None
        if len(dbs) == max_result + 1:
            last_item = dbs.pop(len(dbs) - 1)
            next_token = NextTokenUtils.encode(
                id=last_item.id, date=last_item.gmt_created
            )

        # 分别统计每个统计知识库的文档数量和会话数量
        data = []
        if len(dbs) > 0:
            kb_ids = [obj.kb_id for obj in dbs]
            data = [KnowledgeBaseListResponse.from_orm_model(obj) for obj in dbs]

            # 批量查询文档数量和会话数量
            document_counts = (
                self.kb_documents_repository.batch_count_documents_by_kb_ids(kb_ids)
            )
            session_counts = self.kb_sessions_repository.batch_count_sessions_by_kb_ids(
                kb_ids
            )

            # 设置统计结果
            for kb in data:
                kb.document_count = document_counts.get(kb.kb_id, 0)
                kb.session_count = session_counts.get(kb.kb_id, 0)

        return KnowledgeBaseListPaginationResponse(
            data=data,
            max_results=count,
            next_token=next_token,
        )

    """查询知识库详情"""

    def get_knowledge_base(
        self,
        kb_id: str,
        auth_context: AuthContext,
    ) -> KnowledgeBaseDetailResponse:

        # 获取知识库详情
        knowledge_base = self._get_knowledge_base_by_id(kb_id, auth_context)
        CheckUtils.object_exists_if(knowledge_base is not None, "knowledge_base")

        # 查询知识库的文档数量和会话数量
        document_count = self.kb_documents_repository.count_documents_by_kb(kb_id)
        session_count = self.kb_sessions_repository.count_sessions(kb_id=kb_id)
        knowledge_base.document_count = document_count
        knowledge_base.session_count = session_count

        return knowledge_base

    """修改知识库信息"""

    def update_knowledge_base(
        self,
        kb_id: str,
        auth_context: AuthContext,
        name: Optional[str] = None,
        description: Optional[str] = None,
    ):
        """
        修改知识库信息（支持 Saga 事务）
        """

        # 获取知识库详情
        knowledge_base = self._get_knowledge_base_by_id(kb_id, auth_context)

        # 如果更新名称，检查名称是否重复
        if name is not None and name != knowledge_base.name:
            existing = self.knowledgebase_repository.list_knowledge_bases(
                owner_ali_uid=auth_context.ali_uid,
                owner_wy_id=auth_context.wy_id,
                name=name,
            )
            CheckUtils.object_duplicate_if(
                len(existing) == 0 or existing[0].kb_id == kb_id, "knowledge_base"
            )

        # 保存原始值用于补偿操作
        original_name = knowledge_base.name
        original_description = knowledge_base.description

        # 调用 RAG 服务更新知识库
        logger.info(f"调用 RAG 服务更新知识库: {kb_id}")
        client = create_rag_client()
        update_rag_response = client.update_kb(
            kb_id=kb_id,
            name=name,
            description=description,
        )
        if not update_rag_response or not update_rag_response.body:
            raise ValueError("更新知识库失败")

        # 通过事务更新知识库
        self._update_knowledge_base_with_transaction(
            kb_id=kb_id,
            name=name,
            description=description,
            original_name=original_name,
            original_description=original_description,
            auth_context=auth_context,
            client=client,
        )

    @saga_transactional(rollback_on=Exception)  # 排除 ValueError，只对系统异常回滚
    def _update_knowledge_base_with_transaction(
        self,
        kb_id: str,
        name: str,
        description: Optional[str],
        original_name: str,
        original_description: Optional[str],
        client,
        **kwargs,
    ) -> str:
        """
        使用事务创建知识库的本地部分
        """
        # 添加补偿操作：如果后续步骤失败，删除 RAG 知识库
        _saga_manager = kwargs.get("_saga_manager")
        if _saga_manager:

            def restore_rag_kb():
                try:
                    logger.info(f"执行补偿操作：恢复 RAG 知识库 {kb_id} 的原始值")
                    client.update_kb(
                        kb_id=kb_id,
                        name=original_name,
                        description=original_description,
                    )
                    logger.info(f"补偿操作成功：恢复 RAG 知识库 {kb_id} 的原始值")
                except Exception as e:
                    logger.error(f"补偿操作失败：无法恢复 RAG 知识库 {kb_id}: {e}")

            _saga_manager.add_compensation_action(restore_rag_kb)

        # 步骤2: 更新本地数据库
        logger.info(f"更新本地数据库记录: {kb_id}")
        self.knowledgebase_repository.update_knowledge_base(
            kb_id=kb_id,
            name=name,
            description=description,
            is_update_selective=False,
        )

        logger.info(f"知识库更新成功: {kb_id}")
        return kb_id

    """删除知识库"""

    def delete_knowledge_base(
        self,
        kb_id: str,
        auth_context: AuthContext,
    ):

        # 检查参数
        CheckUtils.parameter_not_null(kb_id, "kb_id")

        # 获取知识库详情
        knowledge_base = self._get_knowledge_base_by_id(
            kb_id=kb_id, auth_context=auth_context, is_check_exists=False
        )
        if knowledge_base is None:
            pass

        # 调用 RAG 服务删除知识库
        logger.info(f"调用 RAG 服务删除知识库: {kb_id}")
        client = create_rag_client()
        delete_rag_response = client.delete_kb(kb_id=kb_id)
        if not delete_rag_response or not delete_rag_response.body:
            raise ValueError("删除知识库失败")

        # 软删除本地数据库记录
        logger.info(f"软删除本地数据库记录: {kb_id}")
        self.knowledgebase_repository.soft_delete_knowledge_base(kb_id)
        self.kb_sessions_repository.soft_delete_sessions(kb_id=kb_id)
        self.kb_document_relations_repository.soft_delete_relations(kb_id=kb_id)

        # 删除资源鉴权
        logger.info(f"注册资源鉴权: {kb_id}")
        auth_service.unregister_resource(
            context=auth_context,
            resource_type=ResourceType.KNOWLEDGE_BASE,
            resource_id=kb_id,
        )

        logger.info(f"知识库删除成功: {kb_id}")

    """内部方法，获取知识库详情"""

    def _get_knowledge_base_by_id(
        self, kb_id: str, auth_context: AuthContext, is_check_exists: bool = True
    ) -> Optional[KnowledgeBaseDetailResponse]:
        """
        获取知识库详情
        """
        # 检查参数
        CheckUtils.parameter_not_null(kb_id, "kb_id")

        # 获取知识库详情
        knowledge_base = self.knowledgebase_repository.get_knowledge_base_by_id(kb_id)

        if is_check_exists:
            CheckUtils.object_exists_if(knowledge_base is not None, "knowledge_base")
        elif knowledge_base is None:
            return None
        response = KnowledgeBaseDetailResponse.from_orm_model(knowledge_base)

        # 权限检查
        auth_service.check_resource_permission(
            context=auth_context,
            resource_type=ResourceType.KNOWLEDGE_BASE,
            resource_id=str(kb_id),
            required_permission=PermissionType.get_owner_permissions(),
        )
        return response

    # ==================== 知识库文档操作 ====================

    def create_documents(
        self,
        auth_context: AuthContext,
        kb_id: str,
        document_list: List[KnowledgeBaseDocumentItem],
    ):
        """
        创建文档

        Args:
            auth_context: 用户权限
            kb_id: 知识库ID
            document_list: 文档列表，每个文档包含 oss_path 和 file_name
        """
        # 检查参数
        CheckUtils.parameter_not_null(kb_id, "kb_id")
        CheckUtils.parameter_not_empty(document_list, "document_list")
        CheckUtils.parameter_not_null(auth_context.ali_uid, "ali_uid")
        CheckUtils.parameter_not_null(auth_context.wy_id, "wy_id")
        for document in document_list:
            CheckUtils.parameter_not_empty(document.oss_path, "oss_path")
            CheckUtils.parameter_not_empty(document.file_id, "file_id")
            CheckUtils.parameter_not_empty(document.file_name, "file_name")

        # 获取知识库详情
        self._get_knowledge_base_by_id(kb_id, auth_context)

        # 调用 RAG 服务创建文档
        documents = []
        for document in document_list:
            # 检查响应
            obj = self._create_rag_document(
                auth_context,
                kb_id,
                document.file_name,
                document.oss_path,
                document.file_id,
            )
            documents.append(obj)

        # 批量插入文档
        self.kb_documents_repository.batch_insert_documents(documents)

        # 批量插入文档关联关系
        self.kb_document_relations_repository.batch_insert_document_relations(
            kb_id=kb_id,
            file_ids=[document.file_id for document in documents],
        )

    @staticmethod
    def _create_rag_document(
        auth_context: AuthContext,
        kb_id: str,
        file_name: str,
        oss_path: str,
        file_id: str = None,
    ) -> KbDocumentModel:
        """
        调用RAG服务创建文档
        """
        try:
            # 调用 RAG 服务提交文档
            client = create_rag_client()
            response = client.submit_document_by_url(
                url=oss_path,
                filename=file_name,
                kb_ids=[kb_id],
                ali_uid=str(auth_context.ali_uid),
                wy_id=auth_context.wy_id,
            )
            if (
                response
                and response.body
                and response.body.data
                and response.body.data.doc_id
            ):
                document = KbDocumentModel(
                    doc_id=response.body.data.doc_id,
                    oss_bucket=response.body.data.infos.bucket,
                    oss_object_name=response.body.data.file_paths.raw,
                    file_title=response.body.data.title,
                    file_size=response.body.data.infos.filesize,
                    status="processing",
                    file_id=file_id,  # 使用传入的file_id默认值
                )
                return document
            else:
                raise ValueError("RAG服务返回数据异常")
        except Exception as e:
            logger.error(f"创建文档失败: {e}")
            raise

    def list_documents(
        self,
        auth_context: AuthContext,
        kb_id: str,
        max_results: int,
        next_token: str,
    ) -> KnowledgeBaseDocumentListPaginationResponse:
        """
        查询文档列表
        """
        # 检查参数
        CheckUtils.parameter_not_null(kb_id, "kb_id")

        # 获取知识库详情
        self._get_knowledge_base_by_id(kb_id, auth_context)

        # 解析 next_token
        pagination_model = NextTokenUtils.decode(next_token)
        less_than_equal_gmt_create = pagination_model.gmt
        less_than_equal_id = pagination_model.id

        # 分页搜索知识库文档关联
        relations = self.kb_document_relations_repository.list_relations(
            kb_id=kb_id,
            session_id_is_null=True,
            limit=max_results + 1,
            less_than_equal_id=less_than_equal_id,
            less_than_equal_gmt_create=less_than_equal_gmt_create,
            order_pairs=[
                KbDocumentRelationModel.gmt_created.desc(),
                KbDocumentRelationModel.id.desc(),
            ],
        )

        # 统计知识库文档关联数量
        count = self.kb_document_relations_repository.count_relations(
            kb_id=kb_id,
            session_id_is_null=True,
        )

        # 生成 next_token
        next_token = None
        if len(relations) == max_results + 1:
            last_item = relations.pop(len(relations) - 1)
            next_token = NextTokenUtils.encode(
                id=last_item.id, date=last_item.gmt_created
            )

        # 搜索文档信息
        data = []
        if len(relations) > 0:
            documents = self.kb_documents_repository.list_kb_documents(
                file_id_list=[obj.file_id for obj in relations],
            )
            data = [
                KnowledgeBaseDocumentListResponse.from_orm_model(obj)
                for obj in documents
            ]

        # 返回分页结果
        return KnowledgeBaseDocumentListPaginationResponse(
            data=data,
            max_results=count,
            next_token=next_token,
        )

    def describe_document(
        self,
        auth_context: AuthContext,
        kb_id: str,
        file_id: str,
    ) -> KnowledgeBaseDocumentDescribeResponse:
        """
        描述文档
        """
        # 检查参数
        CheckUtils.parameter_not_null(kb_id, "kb_id")
        CheckUtils.parameter_not_null(file_id, "document_id")

        # 获取知识库详情
        self._get_knowledge_base_by_id(kb_id, auth_context, is_check_exists=False)

        # 获取文档详情
        document = self.kb_documents_repository.get_kb_document_by_id(file_id)
        CheckUtils.object_exists_if(document is not None, "document")

        # 返回文档详情
        return KnowledgeBaseDocumentDescribeResponse(
            document_id=document.doc_id,
            oss_path=document.oss_object_name,
            file_name=document.file_title,
            status=document.status,
            gmt_created=TimeUtils.to_iso8601_utc(document.gmt_created),
            gmt_modified=TimeUtils.to_iso8601_utc(document.gmt_modified),
        )

    def delete_documents(
        self,
        auth_context: AuthContext,
        kb_id: str,
        document_id_list: List[str],
    ):
        """
        删除文档
        """
        # 检查参数
        CheckUtils.parameter_not_null(kb_id, "kb_id")
        CheckUtils.parameter_not_empty(document_id_list, "document_id_list")

        # 获取文档关联
        relations = self.kb_document_relations_repository.list_relations(
            kb_id=kb_id,
            file_id_list=document_id_list,
        )
        if len(relations) > 0:
            # 删除文档关联
            self.kb_document_relations_repository.soft_delete_relations(
                kb_id=kb_id,
                file_ids=document_id_list,
            )

            # 查询文档的doc ID列表
            documents = self.kb_documents_repository.list_kb_documents(
                file_id_list=document_id_list,
            )

            # 删除RAG服务中的文档关联
            doc_ids = [doc.doc_id for doc in documents]
            if len(doc_ids) > 0:
                client = create_rag_client()
                client.modify_documents_kb_async(
                    kb_id=kb_id,
                    doc_ids=doc_ids,
                    operation="unbind",
                )

    # ==================== 知识库会话操作 ====================

    def create_sessions(
        self,
        auth_context: AuthContext,
        kb_id: str,
        session_id: str,
        message_list: Optional[List[str]] = None,
        file_list: Optional[List[str]] = None,
    ):
        """
        创建会话

        """
        # 检查参数
        CheckUtils.parameter_not_empty(kb_id, "kb_id")
        CheckUtils.parameter_not_empty(session_id, "session_id")

        # 获取知识库详情
        self._get_knowledge_base_by_id(kb_id, auth_context, is_check_exists=True)

        # 判断会话是否添加过知识库
        session = self.kb_sessions_repository.get_session(
            kb_id=kb_id,
            session_id=session_id,
        )
        CheckUtils.object_not_exists(session, "session")

        # 获取每个文件和它的下载地址

        # 处理消息列表
        logger.info(f"处理 知识库{kb_id} 会话{session_id} 消息列表: {message_list}")
        self._process_message_list(auth_context, kb_id, session_id, message_list)

        # 处理文件列表
        logger.info(f"处理 知识库{kb_id} 会话{session_id} 文件列表: {file_list}")
        self._process_file_list(auth_context, kb_id, session_id, file_list)

    def _process_message_list(
        self,
        auth_context: AuthContext,
        kb_id: str,
        session_id: str,
        message_list: Optional[List[str]],
    ):
        snippet_id = self._process_rag_snippet(auth_context, kb_id, message_list)

        # 持久化保存会话和日志数据
        logger.info(f"持久化保存 知识库{kb_id} 会话{session_id} 数据: {snippet_id}")
        self.kb_sessions_repository.create_session(
            kb_id=kb_id,
            session_id=session_id,
            message_id_list=",".join(message_list) if message_list else None,
            snippet_id=snippet_id,
            snippet_status=(
                KbState.PROCESSING.value
                if snippet_id is not None
                else KbState.SUCCESS.value
            ),
        )
        self.kb_operation_logs_repository.insert_log(
            kb_id=kb_id,
            ali_uid=auth_context.ali_uid,
            wy_id=auth_context.wy_id,
            operation_type=KbOperationType.CREATE.value,
            target_type=KbTargetType.SESSION.value,
            target_id=session_id,
        )

    @staticmethod
    def _process_rag_snippet(
        auth_context: AuthContext,
        kb_id: str,
        message_list: Optional[List[str]],
    ):
        """
        处理RAG片段
        """
        rag_messages = []
        snippet_id = None
        if message_list:
            messages = memory_sdk.get_message_by_id_list(message_list)
            for message in messages:
                rag_message = rag_models.SubmitSnippetRagRequestMessages(
                    message_id=message.message_id,
                    content=message.content,
                    role=(
                        message.role.value
                        if hasattr(message.role, "value")
                        else str(message.role)
                    ),
                )
                rag_messages.append(rag_message)

            # 调用 RAG 服务推送会话
            if rag_messages:
                client = create_rag_client()
                response = client.submit_snippet(
                    kb_ids=[kb_id],
                    messages=rag_messages,
                    ali_uid=str(auth_context.ali_uid),
                    wy_id=auth_context.wy_id,
                )
                snippet_id = response.body.data.snippet_id
        return snippet_id

    def _process_file_list(
        self,
        auth_context: AuthContext,
        kb_id: str,
        session_id: str,
        file_id_list: Optional[List[str]],
    ):
        """
        处理文件列表
        """
        # 检查参数
        CheckUtils.parameter_not_empty(kb_id, "kb_id")
        CheckUtils.parameter_not_empty(session_id, "session_id")
        if file_id_list is None or len(file_id_list) == 0:
            return

        # 判断是否有重复文件
        current_documents = self.kb_documents_repository.list_kb_documents(
            session_id=session_id,
            file_id_list=file_id_list,
        )
        current_documents_set = set(document.file_id for document in current_documents)
        new_file_list = [
            file_id for file_id in file_id_list if file_id not in current_documents_set
        ]

        # 处理文件列表
        new_document_list = []
        response = file_service.get_download_urls_by_artifact_ids(
            artifact_ids=new_file_list
        )
        if response and response.download_links:
            for download_link in response.download_links:
                document = self._create_rag_document(
                    auth_context,
                    kb_id,
                    download_link.file_name,
                    download_link.download_url,
                    download_link.artifact_id,
                )
                new_document_list.append(document)

        # 持久化保存新文档数据
        for document in new_document_list:
            self.kb_documents_repository.create_kb_document(
                doc_id=document.doc_id,
                oss_bucket=document.oss_bucket,
                oss_object_name=document.oss_object_name,
                file_title=document.file_title,
                file_size=document.file_size,
                status=document.status,
                file_id=document.file_id,
                session_id=session_id,
            )

        # 持久化保存关联
        self.kb_document_relations_repository.batch_create_relations(
            kb_id=kb_id,
            file_ids=file_id_list,
            session_id=session_id,
        )

    def list_sessions(
        self,
        auth_context: AuthContext,
        kb_id: str,
        max_results: int,
        next_token: str,
    ) -> KnowledgeBaseSessionPaginationResponse:
        """
        查询会话列表
        """
        # 检查参数
        CheckUtils.parameter_not_empty(kb_id, "kb_id")
        CheckUtils.parameter_not_null(max_results, "max_results")

        # 获取知识库详情
        self._get_knowledge_base_by_id(kb_id, auth_context)

        # 解析 next_token
        pagination_model = NextTokenUtils.decode(next_token)
        less_than_equal_gmt_create = pagination_model.gmt
        less_than_equal_id = pagination_model.id

        # 分页搜索知识库文档关联
        sessions = self.kb_sessions_repository.list_sessions(
            kb_id=kb_id,
            limit=max_results + 1,
            less_than_equal_id=less_than_equal_id,
            less_than_equal_gmt_create=less_than_equal_gmt_create,
            order_pairs=[
                KbSessionModel.gmt_created.desc(),
                KbSessionModel.id.desc(),
            ],
        )

        # 统计知识库会话数量
        count = self.kb_sessions_repository.count_sessions(
            kb_id=kb_id,
        )

        # 生成 next_token
        next_token = None
        if len(sessions) == max_results + 1:
            last_item = sessions.pop(len(sessions) - 1)
            next_token = NextTokenUtils.encode(
                id=last_item.id, date=last_item.gmt_created
            )

        # 搜索会话信息
        session_id_list = [obj.session_id for obj in sessions]
        sessions = self.kb_sessions_repository.list_sessions(
            kb_id=kb_id,
            session_id_list=session_id_list,
        )

        # 封装输出结果
        data = []
        if len(sessions) > 0:
            data = [
                KnowledgeBaseSessionListResponse.from_orm_model(obj) for obj in sessions
            ]
            # 查询会话的名称列表
            import src.domain.services.session_service

            session_infos = (
                src.domain.services.session_service.session_service.get_sessions_by_ids(
                    session_ids=session_id_list
                )
            )

            # 批量查询会话的文件大小
            file_sizes = (
                self.kb_documents_repository.batch_sum_file_size_by_session_ids(
                    kb_id=kb_id, session_ids=session_id_list
                )
            )

            # 填充会话信息
            for session in data:
                # 设置会话的名称
                for session_info in session_infos:
                    if session_info.session_id == session.session_id:
                        session.session_name = session_info.title
                        break

                # 设置文件大小
                session.file_size = file_sizes.get(session.session_id, 0)

        # 返回分页结果
        return KnowledgeBaseSessionPaginationResponse(
            data=data,
            max_results=count,
            next_token=next_token,
        )

    def get_kb_session_message_ids(
        self,
        auth_context: AuthContext,
        kb_id: str,
        session_id: str,
    ) -> List[str]:
        """
        获取指定知识库关联会话的所有消息ID列表

        Args:
            auth_context: 认证上下文
            kb_id: 知识库ID
            session_id: 会话ID

        Returns:
            List[str]: 消息ID列表

        Raises:
            ParameterException: 参数错误
            ObjectNotFoundException: 会话不存在
        """
        # 检查参数
        CheckUtils.parameter_not_empty(kb_id, "kb_id")
        CheckUtils.parameter_not_empty(session_id, "session_id")

        # 获取知识库详情（验证权限）
        self._get_knowledge_base_by_id(kb_id, auth_context)

        # 获取会话详情
        session = self.kb_sessions_repository.get_session(
            kb_id=kb_id,
            session_id=session_id,
        )
        CheckUtils.object_exists(session, "session")

        # 解析消息ID列表
        if not session.message_id_list:
            return []

        message_id_list = session.message_id_list.split(",")
        # 过滤空字符串
        return [msg_id.strip() for msg_id in message_id_list if msg_id.strip()]

    def list_session_messages(
        self,
        auth_context: AuthContext,
        kb_id: str,
        session_id: str,
        max_results: int,
        next_token: Optional[str] = None,
    ) -> KnowledgeBaseSessionMessagePaginationResponse:
        """
        查询会话消息列表
        """
        # 检查参数
        CheckUtils.parameter_not_empty(kb_id, "kb_id")
        CheckUtils.parameter_not_empty(session_id, "session_id")
        CheckUtils.parameter_not_null(max_results, "max_results")

        # 获取知识库详情
        self._get_knowledge_base_by_id(kb_id, auth_context)

        # 分页搜索知识库文档关联
        session = self.kb_sessions_repository.get_session(
            kb_id=kb_id,
            session_id=session_id,
        )
        CheckUtils.object_exists(session, "session")
        message_id_list = session.message_id_list.split(",")

        # 统计知识库会话数量
        count = len(message_id_list)

        # 解析 next_token
        index = int(next_token) if next_token else 0

        # 获取消息详情
        message_id_list = message_id_list[index : index + max_results]
        messages = memory_sdk.get_message_by_id_list(message_id_list)

        # 生成 next_token
        next_token = None
        if count > index + max_results:
            next_token = str(index + max_results)

        # 返回分页结果
        return KnowledgeBaseSessionMessagePaginationResponse(
            data=[
                KnowledgeBaseSessionMessageListResponse(
                    message_id=message.message_id,
                    role=message.role,
                    content=message.content,
                    gmt_created=TimeUtils.to_iso8601_utc(message.timestamp),
                    gmt_modified=TimeUtils.to_iso8601_utc(message.timestamp),
                )
                for message in messages
            ],
            max_results=count,
            next_token=next_token,
        )

    def list_session_files(
        self,
        auth_context: AuthContext,
        kb_id: str,
        session_id: str,
        max_results: int,
        next_token: str,
    ) -> KnowledgeBaseSessionFilePaginationResponse:
        """
        查询会话文件列表
        """
        # 检查参数
        CheckUtils.parameter_not_empty(kb_id, "kb_id")
        CheckUtils.parameter_not_empty(session_id, "session_id")
        CheckUtils.parameter_not_null(max_results, "max_results")

        # 获取知识库详情
        self._get_knowledge_base_by_id(kb_id, auth_context)

        # 解析 next_token
        pagination_model = NextTokenUtils.decode(next_token)
        less_than_equal_gmt_create = pagination_model.gmt
        less_than_equal_id = pagination_model.id

        # 分页搜索知识库文档关联
        relations = self.kb_document_relations_repository.list_relations(
            kb_id=kb_id,
            session_id=session_id,
            limit=max_results + 1,
            less_than_equal_id=less_than_equal_id,
            less_than_equal_gmt_create=less_than_equal_gmt_create,
            order_pairs=[
                KbDocumentRelationModel.gmt_created.desc(),
                KbDocumentRelationModel.id.desc(),
            ],
        )

        # 统计知识库文档关联数量
        count = self.kb_document_relations_repository.count_relations(
            kb_id=kb_id,
            session_id=session_id,
        )

        # 生成 next_token
        next_token = None
        if len(relations) == max_results + 1:
            last_item = relations.pop(len(relations) - 1)
            next_token = NextTokenUtils.encode(
                id=last_item.id, date=last_item.gmt_created
            )

        # 搜索文档信息
        data = []
        if len(relations) > 0:
            # 搜索文档信息
            file_id_list = [obj.file_id for obj in relations]
            documents = self.kb_documents_repository.list_kb_documents(
                file_id_list=file_id_list,
            )
            data = [
                KnowledgeBaseSessionFileListResponse.from_orm_model(obj)
                for obj in documents
            ]

        # 返回分页结果
        return KnowledgeBaseSessionFilePaginationResponse(
            data=data,
            max_results=count,
            next_token=next_token,
        )

    def list_session_document_relations(
        self,
        auth_context: AuthContext,
        kb_id: str,
        session_id: str,
    ) -> List[str]:
        """
        获取会话下和知识库关联的所有文档file_id列表
        """
        # 检查参数
        CheckUtils.parameter_not_empty(kb_id, "kb_id")
        CheckUtils.parameter_not_empty(session_id, "session_id")

        # 获取知识库详情
        self._get_knowledge_base_by_id(kb_id, auth_context)

        # 获取所有关联
        relations = self.kb_document_relations_repository.list_relations(
            kb_id=kb_id,
            session_id=session_id,
            limit=None,  # 不限制数量
            less_than_equal_id=None,
            less_than_equal_gmt_create=None,
            order_pairs=None,
        )

        # 只返回file_id列表
        file_ids = [relation.file_id for relation in relations]

        return file_ids

    def get_document_preview_url(
        self,
        auth_context: AuthContext,
        file_id: str,
        expires_in: int = 3600,
    ) -> Optional[Tuple[str, str, int]]:
        """
        根据file_id获取文档预览链接

        Args:
            auth_context: 认证上下文
            file_id: 文件ID
            expires_in: 链接过期时间（秒），默认1小时

        Returns:
            Optional[Tuple[str, str, int]]: (文件名, 预览URL, 过期时间)，失败时返回None
        """
        # 检查参数
        CheckUtils.parameter_not_empty(file_id, "file_id")

        try:
            # 1. 通过file_id查询文档信息
            document = self.kb_documents_repository.get_kb_document_by_id(file_id)
            if not document:
                logger.warning(f"[KnowledgeService] 文档不存在: file_id={file_id}")
                return None

            # 2. 检查文档状态（只有成功状态的文档才能预览）
            if document.status != "success":
                logger.warning(f"[KnowledgeService] 文档状态不支持预览: file_id={file_id}, status={document.status}")
                return None

            # 3. 检查OSS对象名称
            if not document.oss_object_name:
                logger.warning(f"[KnowledgeService] 文档缺少OSS对象名称: file_id={file_id}")
                return None

            # 4. 生成预览链接
            from src.infrastructure.oss.oss_service import oss_service
            preview_url = oss_service.generate_rag_presigned_url_with_preview(
                key=document.oss_object_name,
                expires_in=expires_in
            )

            if not preview_url:
                logger.error(f"[KnowledgeService] 生成预览链接失败: file_id={file_id}")
                return None

            logger.info(f"[KnowledgeService] 生成文档预览链接成功: file_id={file_id}, expires_in={expires_in}s")
            return document.file_title, preview_url, expires_in

        except Exception as e:
            logger.error(f"[KnowledgeService] 获取文档预览链接异常: file_id={file_id}, error={e}")
            return None

    def update_session(
        self,
        auth_context: AuthContext,
        kb_id: str,
        session_id: str,
        message_list: Optional[List[str]] = None,
        file_list: Optional[List[str]] = None,
    ):
        """
        更新会话
        """
        # 检查参数
        CheckUtils.parameter_not_empty(kb_id, "kb_id")
        CheckUtils.parameter_not_empty(session_id, "session_id")

        # 获取知识库详情
        self._get_knowledge_base_by_id(kb_id, auth_context, is_check_exists=True)

        # 查询会话详情
        session = self.kb_sessions_repository.get_session(
            kb_id=kb_id,
            session_id=session_id,
        )
        CheckUtils.object_exists(session, "session")
        CheckUtils.object_state(
            session.session_status != KbState.PROCESSING.value, "Session"
        )

        # 判断message_id_list是否发生变化
        self._process_update_message_list(auth_context, kb_id, session, message_list)

        # 判断file_list是否有变化
        self._process_update_file_list(auth_context, kb_id, session, file_list)

    def _process_update_message_list(
        self,
        auth_context: AuthContext,
        kb_id: str,
        session: KbSessionModel,
        message_list: List[str],
    ):
        """
        处理消息列表
        """
        current_message_list = (
            session.message_id_list.split(",") if session.message_id_list else []
        )
        if current_message_list != (message_list or []):
            snippet_id = self._process_rag_snippet(auth_context, kb_id, message_list)
            session.snippet_id = snippet_id
            session.message_id_list = ",".join(message_list) if message_list else None
            session.snippet_status = KbState.PROCESSING.value
            session.session_status = KbState.PROCESSING.value
            self.kb_sessions_repository.update_session_by_id(session)

    def _process_update_file_list(
        self,
        auth_context: AuthContext,
        kb_id: str,
        session: KbSessionModel,
        file_list: List[str],
    ):
        """
        处理文件列表
        """
        relations = self.kb_document_relations_repository.list_relations(
            kb_id=kb_id,
            session_id=session.session_id,
        )
        cur_file_list = [obj.file_id for obj in relations]
        new_file_list = [file_id for file_id in file_list] if file_list else []
        if cur_file_list != new_file_list:
            # 获取待删除的文件
            delete_file_list = set(cur_file_list) - set(new_file_list)
            # 获取待新增的文件
            add_file_list = set(new_file_list) - set(cur_file_list)

            # 调用rag服务删除文件
            if delete_file_list:
                client = create_rag_client()
                for file_id in delete_file_list:
                    client.delete_document(
                        doc_id=file_id,
                    )
                # 批量软删除关联
                for file_id in delete_file_list:
                    self.kb_document_relations_repository.soft_delete_relation(
                        kb_id=kb_id,
                        file_id=file_id,
                    )

            # 调用rag服务新增文件
            if add_file_list:
                self._process_file_list(
                    auth_context=auth_context,
                    kb_id=kb_id,
                    file_id_list=[str(file_id) for file_id in add_file_list],
                    session_id=session.session_id,
                )

    def delete_session(
        self,
        auth_context: AuthContext,
        kb_id: str,
        session_id: str,
    ):
        """
        删除会话（单个会话）
        """
        self.delete_sessions(auth_context, kb_id, [session_id])

    def delete_sessions(
        self,
        auth_context: AuthContext,
        kb_id: str,
        session_ids: List[str],
    ):
        """
        批量删除会话
        """
        # 检查参数
        CheckUtils.parameter_not_empty(kb_id, "kb_id")
        CheckUtils.parameter_not_empty(session_ids, "session_ids")

        # 获取知识库详情
        self._get_knowledge_base_by_id(kb_id, auth_context, is_check_exists=True)

        # 获取会话详情列表
        sessions = self.kb_sessions_repository.list_sessions(
            kb_id=kb_id,
            session_id_list=session_ids,
        )
        if not sessions:
            logger.warning(
                f"没有找到要删除的会话: kb_id={kb_id}, session_ids={session_ids}"
            )
            return
        for session in sessions:
            CheckUtils.object_state(
                session.session_status != KbState.PROCESSING.value, "Session"
            )

        # 批量软删除会话
        deleted_count = self.kb_sessions_repository.soft_delete_sessions(
            kb_id=kb_id,
            session_ids=session_ids,
        )
        logger.info(f"批量软删除会话成功: kb_id={kb_id}, 删除数量={deleted_count}")

        # 批量软删除会话关联的文档关系
        self.kb_document_relations_repository.soft_delete_relations(
            kb_id=kb_id,
            session_ids=session_ids,
        )

        # 批量记录操作日志
        logs_data = [
            {
                "kb_id": kb_id,
                "ali_uid": auth_context.ali_uid,
                "wy_id": auth_context.wy_id,
                "operation_type": KbOperationType.DELETE.value,
                "target_type": KbTargetType.SESSION.value,
                "target_id": session_id,
                "status": KbState.SUCCESS.value,
            }
            for session_id in session_ids
        ]
        if logs_data:
            self.kb_operation_logs_repository.batch_insert_logs(logs_data)

        # 批量调用 RAG 服务删除会话信息
        client = create_rag_client()
        snippet_ids = [session.snippet_id for session in sessions if session.snippet_id]
        for snippet_id in snippet_ids:
            try:
                client.delete_snippet(snippet_id=snippet_id)
                logger.info(f"删除 RAG 会话片段成功: snippet_id={snippet_id}")
            except Exception as e:
                logger.error(
                    f"删除 RAG 会话片段失败: snippet_id={snippet_id}, error={e}"
                )

    # ==================== 知识库日志操作 ====================

    def list_logs(
        self,
        auth_context: AuthContext,
        kb_id: str,
        max_results: int,
        next_token: str,
    ) -> KnowledgeBaseLogListPaginationResponse:
        """
        查询日志列表
        """

        # 解析 next_token
        pagination_model = NextTokenUtils.decode(next_token)
        less_than_equal_gmt_create = pagination_model.gmt
        less_than_equal_id = pagination_model.id

        # 获取知识库详情
        if kb_id:
            self._get_knowledge_base_by_id(kb_id, auth_context, is_check_exists=True)

        # 查询日志列表
        logs = self.kb_operation_logs_repository.list_logs(
            ali_uid=auth_context.ali_uid,
            wy_id=auth_context.wy_id,
            kb_id=kb_id,
            limit=max_results + 1,
            less_than_equal_id=less_than_equal_id,
            less_than_equal_gmt_create=less_than_equal_gmt_create,
            order_pairs=[
                KbOperationLogModel.gmt_created.desc(),
                KbOperationLogModel.id.desc(),
            ],
        )

        # 统计日志数量
        count = self.kb_operation_logs_repository.count_logs(
            ali_uid=auth_context.ali_uid,
            wy_id=auth_context.wy_id,
            kb_id=kb_id,
        )

        # 生成 next_token
        next_token = None
        if len(logs) == max_results + 1:
            last_item = logs.pop(len(logs) - 1)
            next_token = NextTokenUtils.encode(
                id=last_item.id, date=last_item.gmt_created
            )

        # 查询相关补充信息
        data = []
        if len(logs) > 0:
            data = [KnowledgeBaseLogListResponse.from_orm_model(obj) for obj in logs]
            self._fill_log_data(auth_context, data)

        return KnowledgeBaseLogListPaginationResponse(
            data=data,
            max_results=count,
            next_token=next_token,
        )

    def _fill_log_data(
        self, auth_context: AuthContext, data: List[KnowledgeBaseLogListResponse]
    ):
        """
        填充日志数据
        """

        # 查询知识库信息
        kb_ids = [obj.kb_id for obj in data]
        kbs = self.knowledgebase_repository.list_knowledge_bases(
            owner_ali_uid=auth_context.ali_uid,
            owner_wy_id=auth_context.wy_id,
            kb_ids=kb_ids,
            is_all=True,
        )
        kb_dict = {kb.kb_id: kb for kb in kbs}
        for obj in data:
            obj.kb_name = kb_dict[obj.kb_id].name if obj.kb_id else None

        # 查询会话信息
        session_id_list = [
            obj.target_id
            for obj in data
            if obj.target_type == KbTargetType.SESSION.value
        ]
        import src.domain.services.session_service

        session_infos = (
            src.domain.services.session_service.session_service.get_sessions_by_ids(
                session_ids=session_id_list
            )
        )
        session_dict = {session.session_id: session for session in session_infos}
        for obj in data:
            if obj.target_type == KbTargetType.SESSION.value:
                obj.title = session_dict[obj.target_id].title

        # 查询文档信息
        file_id_list = [
            obj.target_id
            for obj in data
            if obj.target_type == KbTargetType.DOCUMENT.value
        ]
        documents = self.kb_documents_repository.list_kb_documents(
            file_id_list=file_id_list,
        )
        document_dict = {document.file_id: document for document in documents}
        for obj in data:
            if obj.target_type == KbTargetType.DOCUMENT.value:
                obj.title = document_dict[obj.target_id].file_title

        return data

    # ==================== 知识库知识更新操作 ====================

    def update_knowledge_base_status(
        self,
    ):
        """
        更新知识库资源状态
        """
        # 同步文档状态
        self._update_documents_status()

        # 同步会话解析状态
        self._update_snippet_status()

        # 同步会话状态
        self._update_session_status()

        # 同步日志状态
        self._update_log_status()

    def _update_documents_status(self) -> None:
        """
        更新知识库文档状态，如果状态为SUCCESS或者FAILED，则更新kb_documents表
        """
        logger.info("[KnowledgeService] 更新知识库文档状态")
        client = create_rag_client()
        documents = self.kb_documents_repository.list_kb_documents(
            status=KbState.PROCESSING.value,
        )
        document_dict = {document.doc_id: document for document in documents}
        document_ids = [obj.doc_id for obj in documents]

        # 查询RAG服务每个文档的状态，如果状态为FINISHED，则更新kb_documents表中status为FINISHED
        if document_ids:
            response = client.check_docs_status(
                doc_ids=document_ids,
            )
            if response and response.body and response.body.data:
                for rag_obj in response.body.data:
                    if (
                        rag_obj.doc_id in document_dict
                        and rag_obj.infos
                        and rag_obj.infos.final_state
                        and (
                            rag_obj.infos.final_state == KbState.SUCCESS.value
                            or rag_obj.infos.final_state == KbState.FAILED.value
                        )
                    ):
                        logger.info(
                            f"[KnowledgeService] 更新文档状态: doc_id={rag_obj.doc_id}, status={rag_obj.infos.final_state}"
                        )
                        self.kb_documents_repository.update_kb_document(
                            doc_id=rag_obj.doc_id,
                            status=rag_obj.infos.final_state,
                        )

    def _update_snippet_status(self) -> None:
        """
        更新知识库会话解析状态
        """
        logger.info("[KnowledgeService] 更新知识库会话解析状态")
        # 查询 kb_sessions 表中 snippet_status 为 PROCESSING 的会话
        sessions = self.kb_sessions_repository.list_sessions(
            snippet_status=KbState.PROCESSING.value,
        )
        session_dict = {session.snippet_id: session for session in sessions}

        # 查询RAG服务每个snippet的状态，如果状态为FINISHED，则更新kb_sessions表中session_status为FINISHED
        client = create_rag_client()
        snippet_ids = [session.snippet_id for session in sessions if session.snippet_id]
        if snippet_ids:
            response = client.check_snippet_status(snippet_ids=snippet_ids)
            if response and response.body and response.body.data:
                for rag_obj in response.body.data:
                    if (
                        rag_obj.snippet_id in session_dict
                        and rag_obj.infos
                        and rag_obj.infos.final_state
                        and (
                            rag_obj.infos.final_state == KbState.SUCCESS.value
                            or rag_obj.infos.final_state == KbState.FAILED.value
                        )
                    ):
                        session = session_dict[rag_obj.snippet_id]
                        session.snippet_status = rag_obj.infos.final_state
                        logger.info(
                            f"[KnowledgeService] 更新会话状态: session_id={session.session_id}, snippet_status={session.snippet_status}"
                        )
                        self.kb_sessions_repository.update_session_by_id(session)

    def _update_session_status(self) -> None:
        """
        更新知识库会话状态
        """
        logger.info("[KnowledgeService] 更新知识库会话状态")
        # 查询 kb_sessions 表中 session_status 为 PROCESSING, snippet_status 为 FINISHED 的会话
        logger.info("[KnowledgeService] 更新知识库会话总和状态")
        sessions = self.kb_sessions_repository.list_sessions(
            session_status=KbState.PROCESSING.value,
            not_snippet_status=KbState.PROCESSING.value,
        )
        for session in sessions:
            relations = self.kb_document_relations_repository.list_relations(
                session_id=session.session_id,
                kb_id=session.kb_id,
            )
            if len(relations) == 0:
                session.session_status = KbState.SUCCESS.value
                logger.info(
                    f"[KnowledgeService] 会话没有关联文档，更新会话状态: session_id={session.session_id}, session_status={session.session_status}"
                )
                self.kb_sessions_repository.update_session_by_id(session)
            else:
                file_ids = [obj.file_id for obj in relations]
                documents = self.kb_documents_repository.list_kb_documents(
                    file_id_list=file_ids,
                )
                # 如果文档有未处理完成，则跳过
                if any(
                    document.status == KbState.PROCESSING.value
                    for document in documents
                ):
                    logger.info(
                        f"[KnowledgeService] 文档未处理完成，跳过: session_id={session.session_id}"
                    )
                    continue

                # 如果会话解析失败或者文档解析失败，则更新会话状态为FAILED
                # 如果会话解析成功，且所有文档都处理完成，则更新会话状态为SUCCESS
                if session.snippet_status == KbState.FAILED.value or any(
                    document.status == KbState.FAILED.value for document in documents
                ):
                    session.session_status = KbState.FAILED.value
                else:
                    session.session_status = KbState.SUCCESS.value

                logger.info(
                    f"[KnowledgeService] 更新会话状态: session_id={session.session_id}, session_status={session.session_status}"
                )
                self.kb_sessions_repository.update_session_by_id(session)

    def _update_log_status(self) -> None:
        """
        更新知识库日志状态
        """
        logger.info("[KnowledgeService] 更新知识库日志状态")

        # 查询会话日志，查询会话日志状态
        logs = self.kb_operation_logs_repository.list_logs(
            status=KbState.PROCESSING.value,
            target_type=KbTargetType.SESSION.value,
            operation_type=KbOperationType.CREATE.value,
        )
        session_id_list = [log.target_id for log in logs]
        sessions = self.kb_sessions_repository.list_sessions(
            session_id_list=session_id_list,
            not_deleted=False,
        )
        session_dict = {session.session_id: session for session in sessions}
        for log in logs:
            if log.target_id not in session_dict:
                continue
            session = session_dict[log.target_id]
            if (
                session.session_status == KbState.SUCCESS.value
                or session.session_status == KbState.FAILED.value
            ):
                self.kb_operation_logs_repository.update_log_by_id(
                    id=log.id, status=session.session_status
                )

        # 查询文档日志，查询文档日志状态
        logs = self.kb_operation_logs_repository.list_logs(
            status=KbState.PROCESSING.value,
            target_type=KbTargetType.DOCUMENT.value,
            operation_type=KbOperationType.CREATE.value,
        )
        document_ids = [log.target_id for log in logs]
        documents = self.kb_documents_repository.list_kb_documents(
            file_id_list=document_ids,
        )
        document_dict = {document.file_id: document for document in documents}
        for log in logs:
            if log.target_id not in document_dict:
                continue
            document = document_dict[log.target_id]
            if (
                document.status == KbState.SUCCESS.value
                or document.status == KbState.FAILED.value
            ):
                self.kb_operation_logs_repository.update_log_by_id(
                    id=log.id, status=document.status
                )


# 创建全局实例
knowledgebase_service = KnowledgeService()
