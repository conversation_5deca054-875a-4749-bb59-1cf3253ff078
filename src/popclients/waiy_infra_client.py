# -*- coding: utf-8 -*-
"""
WaiyInfraClient
"""
from typing import Optional, Dict, Any, List
import sys
import os

try:
    from alibabacloud_tea_openapi import models as open_api_models
    from alibabacloud_wuyingaiinner20250708 import client
    from alibabacloud_wuyingaiinner20250708 import models as waiy_models
except ImportError as e:
    # 如果无法导入，提供更详细的错误信息
    raise ImportError(
        f"无法导入 alibabacloud_wuyingaiinner20250708 包: {e}\n"
        "请确保已安装: pip install alibabacloud-wuyingaiinner20250708==1.1.0"
    )

from aliyunaklesscredprovider.core import AklessCredproviderFactory
from loguru import logger


class WaiyInfraClient:
    """
    无影AI内部服务客户端封装类 (20250708版本 - pip包版本1.1.0)
    提供简化的接口来访问无影AI内部服务
    使用 alibabacloud-wuyingaiinner20250708==1.1.0 包
    """

    def __init__(
        self,
        endpoint: str = "wuyingaiinner-pre.aliyuncs.com",
        connect_timeout: int = 10000,
        read_timeout: int = 10000,
        **kwargs
    ):
        """
        初始化WaiyInfraClient

        Args:
            endpoint: 服务端点，默认为预发环境
            connect_timeout: 连接超时时间（毫秒）
            read_timeout: 读取超时时间（毫秒）
            **kwargs: 其他配置参数
        """
        # 从配置中读取RAM角色ARN
        try:
            # 尝试多种导入方式
            try:
                from src.shared.config.environments import env_manager
            except ImportError:
                import sys
                import os
                # 添加项目根目录到Python路径
                project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
                if project_root not in sys.path:
                    sys.path.insert(0, project_root)
                from src.shared.config.environments import env_manager

            config = env_manager.get_config()
            ram_role_arn = config.ram_role_arn
            region_id = config.region_id

            if not ram_role_arn:
                raise ValueError("配置中缺少ram_role_arn，无法使用无AK认证")
        except Exception as e:
            logger.error(f"无法从配置中读取RAM角色ARN: {e}")
            raise ValueError(f"无法从配置中读取RAM角色ARN: {e}")

        self.endpoint = endpoint
        self.connect_timeout = connect_timeout
        self.read_timeout = read_timeout
        self.ram_role_arn = ram_role_arn
        self.region_id = region_id

        try:
            from src.shared.auth import get_akless_credential
            cred_client = get_akless_credential(ram_role_arn)
        except Exception as e:
            logger.error(f"创建无AK凭证失败: {e}")
            raise ValueError(f"创建无AK凭证失败: {e}")

        # 创建配置
        self.config = open_api_models.Config(
            credential=cred_client,
            region_id=region_id,
            connect_timeout=connect_timeout,
            read_timeout=read_timeout,
            **kwargs
        )
        # 确保端点被正确设置
        self.config.endpoint = endpoint

        # 初始化客户端
        self._client = client.Client(self.config)

    # 应用管理相关方法
    def list_apps(self) -> waiy_models.ListAppsResponse:
        """
        获取应用列表

        Returns:
            ListAppsResponse: 应用列表响应
        """
        try:
            return self._client.list_apps()
        except Exception as e:
            raise WaiyInfraClientError(f"获取应用列表失败: {str(e)}") from e

    async def list_apps_async(self) -> waiy_models.ListAppsResponse:
        """
        异步获取应用列表

        Returns:
            ListAppsResponse: 应用列表响应
        """
        try:
            return await self._client.list_apps_async()
        except Exception as e:
            raise WaiyInfraClientError(f"异步获取应用列表失败: {str(e)}") from e

    # 同步消息处理方法
    def message(
        self,
        app_id: str,
        message: str,
        context: Optional[waiy_models.MessageRequestContext] = None,
        resources: Optional[List[waiy_models.MessageRequestResources]] = None,
        session_id: Optional[str] = None,
        trace_id: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> waiy_models.MessageResponse:
        """
        同步发送消息

        Args:
            app_id: 应用ID
            message: 消息内容
            context: 上下文信息
            resources: 资源列表
            session_id: 会话ID（如果context为None时使用）
            trace_id: 跟踪ID（如果context为None时使用）
            user_id: 用户ID（如果context为None时使用）

        Returns:
            MessageResponse: 同步消息响应
        """
        try:
            # 如果没有提供context，但提供了session_id等参数，则自动创建context
            if context is None and (session_id or trace_id or user_id):
                context = self.create_sync_message_context(
                    session_id=session_id,
                    trace_id=trace_id,
                    user_id=user_id
                )

            request = waiy_models.MessageRequest(
                app_id=app_id,
                message=message,
                context=context,
                resources=resources
            )
            return self._client.message(request)
        except Exception as e:
            raise WaiyInfraClientError(f"发送同步消息失败: {str(e)}") from e

    async def message_async(
        self,
        app_id: str,
        message: str,
        context: Optional[waiy_models.MessageRequestContext] = None,
        resources: Optional[List[waiy_models.MessageRequestResources]] = None,
        session_id: Optional[str] = None,
        trace_id: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> waiy_models.MessageResponse:
        """
        异步发送消息（同步版本的异步调用）

        Args:
            app_id: 应用ID
            message: 消息内容
            context: 上下文信息
            resources: 资源列表
            session_id: 会话ID（如果context为None时使用）
            trace_id: 跟踪ID（如果context为None时使用）
            user_id: 用户ID（如果context为None时使用）

        Returns:
            MessageResponse: 同步消息响应
        """
        try:
            # 如果没有提供context，但提供了session_id等参数，则自动创建context
            if context is None and (session_id or trace_id or user_id):
                context = self.create_sync_message_context(
                    session_id=session_id,
                    trace_id=trace_id,
                    user_id=user_id
                )

            request = waiy_models.MessageRequest(
                app_id=app_id,
                message=message,
                context=context,
                resources=resources
            )
            logger.info(f"异步发送同步消息: app_id={app_id}, message={message[:50]}..., context={context}")
            return await self._client.message_async(request)
        except Exception as e:
            raise WaiyInfraClientError(f"异步发送同步消息失败: {str(e)}") from e

    # 异步消息处理相关方法
    def message_async_sync(
        self,
        app_id: str,
        message: str,
        context: Optional[waiy_models.MessageAsyncRequestContext] = None,
        resources: Optional[List[waiy_models.MessageAsyncRequestResources]] = None,
        session_id: Optional[str] = None,
        trace_id: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> waiy_models.MessageAsyncResponse:
        """
        异步消息发送（同步调用）

        Args:
            app_id: 应用ID
            message: 消息内容
            context: 上下文信息
            resources: 资源列表
            session_id: 会话ID（如果context为None时使用）
            trace_id: 跟踪ID（如果context为None时使用）
            user_id: 用户ID（如果context为None时使用）

        Returns:
            MessageAsyncResponse: 异步消息响应
        """
        try:
            request = waiy_models.MessageAsyncRequest(
                app_id=app_id,
                message=message,
                context=context,
                resources=resources
            )
            logger.info(f"发送异步消息（同步调用）: app_id={app_id}, message={message[:50]}..., context={context}")
            return self._client.message_async(request)
        except Exception as e:
            raise WaiyInfraClientError(f"发送异步消息失败: {str(e)}") from e

    async def message_async_async(
        self,
        app_id: str,
        message: str,
        context: Optional[waiy_models.MessageAsyncRequestContext] = None,
        resources: Optional[List[waiy_models.MessageAsyncRequestResources]] = None,
        session_id: Optional[str] = None,
        trace_id: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> waiy_models.MessageAsyncResponse:
        """
        异步发送消息（异步版本）

        Args:
            app_id: 应用ID
            message: 消息内容
            context: 上下文信息
            resources: 资源列表
            session_id: 会话ID（如果context为None时使用）
            trace_id: 跟踪ID（如果context为None时使用）
            user_id: 用户ID（如果context为None时使用）

        Returns:
            MessageAsyncResponse: 异步消息响应
        """
        try:
            # 如果没有提供context，但提供了session_id等参数，则自动创建context
            if context is None and (session_id or trace_id or user_id):
                context = self.create_async_message_context(
                    session_id=session_id,
                    trace_id=trace_id,
                    user_id=user_id
                )

            request = waiy_models.MessageAsyncRequest(
                app_id=app_id,
                message=message,
                context=context,
                resources=resources
            )
            logger.info(f"异步发送异步消息: app_id={app_id}, message={message[:50]}..., context={context}")
            return await self._client.message_async_async(request)
        except Exception as e:
            raise WaiyInfraClientError(f"异步发送异步消息失败: {str(e)}") from e
    
    # 便捷方法：创建请求对象
    def create_sync_message_context(
        self,
        runtime_resource: Optional[waiy_models.MessageRequestContextRuntimeResource] = None,
        session_id: Optional[str] = None,
        trace_id: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> waiy_models.MessageRequestContext:
        """
        创建同步消息上下文对象

        Args:
            runtime_resource: 运行时资源
            session_id: 会话ID
            trace_id: 跟踪ID
            user_id: 用户ID

        Returns:
            MessageRequestContext: 同步消息上下文对象
        """
        return waiy_models.MessageRequestContext(
            runtime_resource=runtime_resource,
            session_id=session_id,
            trace_id=trace_id,
            user_id=user_id
        )

    def create_async_message_context(
        self,
        runtime_resource: Optional[waiy_models.MessageAsyncRequestContextRuntimeResource] = None,
        session_id: Optional[str] = None,
        trace_id: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> waiy_models.MessageAsyncRequestContext:
        """
        创建异步消息上下文对象

        Args:
            runtime_resource: 运行时资源
            session_id: 会话ID
            trace_id: 跟踪ID
            user_id: 用户ID

        Returns:
            MessageAsyncRequestContext: 异步消息上下文对象
        """
        return waiy_models.MessageAsyncRequestContext(
            runtime_resource=runtime_resource,
            session_id=session_id,
            trace_id=trace_id,
            user_id=user_id
        )

    # 向后兼容的方法
    def create_message_context(
        self,
        runtime_resource: Optional[waiy_models.MessageAsyncRequestContextRuntimeResource] = None,
        session_id: Optional[str] = None,
        trace_id: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> waiy_models.MessageAsyncRequestContext:
        """
        创建消息上下文对象（向后兼容，默认创建异步消息上下文）

        Args:
            runtime_resource: 运行时资源
            session_id: 会话ID
            trace_id: 跟踪ID
            user_id: 用户ID

        Returns:
            MessageAsyncRequestContext: 消息上下文对象
        """
        return self.create_async_message_context(
            runtime_resource=runtime_resource,
            session_id=session_id,
            trace_id=trace_id,
            user_id=user_id
        )

    def create_sync_message_resource(
        self,
        type: str,
        address: Optional[str] = None,
        content: Optional[str] = None,
        file_name: Optional[str] = None,
        file_size: Optional[int] = None,
        file_type: Optional[str] = None,
        kb_id: Optional[str] = None,
        query_parameters: Optional[Any] = None,
        top_k: Optional[int] = None,
        upload_time: Optional[str] = None
    ) -> waiy_models.MessageRequestResources:
        """
        创建同步消息资源对象

        Args:
            type: 资源类型
            address: 资源地址
            content: 资源内容
            file_name: 文件名
            file_size: 文件大小
            file_type: 文件类型
            kb_id: 知识库ID
            query_parameters: 查询参数
            top_k: Top K值
            upload_time: 上传时间

        Returns:
            MessageRequestResources: 同步消息资源对象
        """
        return waiy_models.MessageRequestResources(
            type=type,
            address=address,
            content=content,
            file_name=file_name,
            file_size=file_size,
            file_type=file_type,
            kb_id=kb_id,
            query_parameters=query_parameters,
            top_k=top_k,
            upload_time=upload_time
        )

    def create_async_message_resource(
        self,
        type: str,
        address: Optional[str] = None,
        content: Optional[str] = None,
        file_name: Optional[str] = None,
        file_size: Optional[int] = None,
        file_type: Optional[str] = None,
        kb_id: Optional[str] = None,
        query_parameters: Optional[Any] = None,
        top_k: Optional[int] = None,
        upload_time: Optional[str] = None
    ) -> waiy_models.MessageAsyncRequestResources:
        """
        创建异步消息资源对象

        Args:
            type: 资源类型
            address: 资源地址
            content: 资源内容
            file_name: 文件名
            file_size: 文件大小
            file_type: 文件类型
            kb_id: 知识库ID
            query_parameters: 查询参数
            top_k: Top K值
            upload_time: 上传时间

        Returns:
            MessageAsyncRequestResources: 异步消息资源对象
        """
        return waiy_models.MessageAsyncRequestResources(
            type=type,
            address=address,
            content=content,
            file_name=file_name,
            file_size=file_size,
            file_type=file_type,
            kb_id=kb_id,
            query_parameters=query_parameters,
            top_k=top_k,
            upload_time=upload_time
        )

    # 向后兼容的方法
    def create_message_resource(
        self,
        type: str,
        address: Optional[str] = None,
        content: Optional[str] = None,
        file_name: Optional[str] = None,
        file_size: Optional[int] = None,
        file_type: Optional[str] = None,
        kb_id: Optional[str] = None,
        query_parameters: Optional[Any] = None,
        top_k: Optional[int] = None,
        upload_time: Optional[str] = None
    ) -> waiy_models.MessageAsyncRequestResources:
        """
        创建消息资源对象（向后兼容，默认创建异步消息资源）

        Args:
            type: 资源类型
            address: 资源地址
            content: 资源内容
            file_name: 文件名
            file_size: 文件大小
            file_type: 文件类型
            kb_id: 知识库ID
            query_parameters: 查询参数
            top_k: Top K值
            upload_time: 上传时间

        Returns:
            MessageAsyncRequestResources: 消息资源对象
        """
        return self.create_async_message_resource(
            type=type,
            address=address,
            content=content,
            file_name=file_name,
            file_size=file_size,
            file_type=file_type,
            kb_id=kb_id,
            query_parameters=query_parameters,
            top_k=top_k,
            upload_time=upload_time
        )

    # 实用方法
    def get_client_info(self) -> Dict[str, Any]:
        """
        获取客户端信息

        Returns:
            Dict[str, Any]: 客户端配置信息
        """
        return {
            "ram_role_arn": self.ram_role_arn,
            "region_id": self.region_id,
            "endpoint": self.endpoint or "wuyingaiinner-pre.aliyuncs.com",
            "connect_timeout": self.connect_timeout,
            "read_timeout": self.read_timeout,
            "version": "20250708-1.1.0"
        }

    def __str__(self) -> str:
        """返回客户端字符串表示"""
        return f"WaiyInfraClient(endpoint={self.endpoint}, ram_role_arn={self.ram_role_arn}, version=20250708-1.1.0)"

    def __repr__(self) -> str:
        """返回客户端详细字符串表示"""
        return self.__str__()


class WaiyInfraClientError(Exception):
    """WaiyInfraClient异常类"""
    pass


# 便捷函数
def create_waiy_infra_client(
    endpoint: Optional[str] = None,
    **kwargs
) -> WaiyInfraClient:
    """
    创建WaiyInfraClient实例的便捷函数

    Args:
        endpoint: 服务端点，如果为None则使用默认值
        **kwargs: 其他配置参数

    Returns:
        WaiyInfraClient: 客户端实例
    """
    # 如果没有指定端点，使用默认的预发环境端点
    if endpoint is None:
        endpoint = "wuyingaiinner-pre.aliyuncs.com"

    return WaiyInfraClient(
        endpoint=endpoint,
        **kwargs
    )